﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddLawsuitClassAndTopic : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "lawsuit_class",
                schema: "harvey",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    type = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    legal_provision = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    article = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    acronym = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    old_acronym = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    active_pole = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    passive_pole = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    glossary = table.Column<string>(type: "text", nullable: false),
                    is_own_numbering = table.Column<bool>(type: "boolean", nullable: false),
                    is_first_instance = table.Column<bool>(type: "boolean", nullable: false),
                    is_second_instance = table.Column<bool>(type: "boolean", nullable: false),
                    just_es_juizado_es = table.Column<bool>(type: "boolean", nullable: false),
                    just_es_turmas = table.Column<bool>(type: "boolean", nullable: false),
                    just_es1grau_mil = table.Column<bool>(type: "boolean", nullable: false),
                    just_es2grau_mil = table.Column<bool>(type: "boolean", nullable: false),
                    just_es_juizado_es_fp = table.Column<bool>(type: "boolean", nullable: false),
                    just_tu_es_un = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed1grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed2grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed_juizado_es = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed_turmas = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed_nacional = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed_regional = table.Column<bool>(type: "boolean", nullable: false),
                    just_trab1grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_trab2grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_trab_tst = table.Column<bool>(type: "boolean", nullable: false),
                    just_trab_csjt = table.Column<bool>(type: "boolean", nullable: false),
                    stf = table.Column<bool>(type: "boolean", nullable: false),
                    stj = table.Column<bool>(type: "boolean", nullable: false),
                    cjf = table.Column<bool>(type: "boolean", nullable: false),
                    cnj = table.Column<bool>(type: "boolean", nullable: false),
                    just_mil_uniao1grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_mil_uniao_stm = table.Column<bool>(type: "boolean", nullable: false),
                    just_mil_est1grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_mil_est_tjm = table.Column<bool>(type: "boolean", nullable: false),
                    just_elei1grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_elei2grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_elei_tse = table.Column<bool>(type: "boolean", nullable: false),
                    item_type = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    included_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    included_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    user_ip = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    updated_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    procedure_id = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    procedure_origin = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    is_criminal = table.Column<bool>(type: "boolean", maxLength: 1, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lawsuit_class", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "lawsuit_topic",
                schema: "harvey",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    legal_provision = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    article = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    glossary = table.Column<string>(type: "text", nullable: false),
                    is_secret = table.Column<bool>(type: "boolean", nullable: false),
                    secondary_topic = table.Column<bool>(type: "boolean", nullable: false),
                    previous_crime = table.Column<bool>(type: "boolean", nullable: false),
                    is_first_instance = table.Column<bool>(type: "boolean", nullable: false),
                    is_second_instance = table.Column<bool>(type: "boolean", nullable: false),
                    just_es_juizado_es = table.Column<bool>(type: "boolean", nullable: false),
                    just_es_turmas = table.Column<bool>(type: "boolean", nullable: false),
                    just_es1grau_mil = table.Column<bool>(type: "boolean", nullable: false),
                    just_es2grau_mil = table.Column<bool>(type: "boolean", nullable: false),
                    just_es_juizado_es_fp = table.Column<bool>(type: "boolean", nullable: false),
                    just_tu_es_un = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed1grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed2grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed_juizado_es = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed_turmas = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed_nacional = table.Column<bool>(type: "boolean", nullable: false),
                    just_fed_regional = table.Column<bool>(type: "boolean", nullable: false),
                    just_trab1grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_trab2grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_trab_tst = table.Column<bool>(type: "boolean", nullable: false),
                    just_trab_csjt = table.Column<bool>(type: "boolean", nullable: false),
                    stf = table.Column<bool>(type: "boolean", nullable: false),
                    stj = table.Column<bool>(type: "boolean", nullable: false),
                    cjf = table.Column<bool>(type: "boolean", nullable: false),
                    cnj = table.Column<bool>(type: "boolean", nullable: false),
                    just_mil_uniao1grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_mil_uniao_stm = table.Column<bool>(type: "boolean", nullable: false),
                    just_mil_est1grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_mil_est_tjm = table.Column<bool>(type: "boolean", nullable: false),
                    just_elei1grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_elei2grau = table.Column<bool>(type: "boolean", nullable: false),
                    just_elei_tse = table.Column<bool>(type: "boolean", nullable: false),
                    item_type = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    updated_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    user_ip = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    updated_by_id = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lawsuit_topic", x => x.id);
                });

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "Other",
                column: "order",
                value: 8);

            migrationBuilder.InsertData(
                schema: "harvey",
                table: "legal_instance",
                columns: new[] { "id", "deleted_at", "display_name", "order", "updated_at" },
                values: new object[,]
                {
                    { "STM", null, "Superior Tribunal Militar", 7, null },
                    { "TSE", null, "Tribunal Superior Eleitoral", 6, null },
                    { "TST", null, "Tribunal Superior de Trabalho", 5, null }
                });

            migrationBuilder.CreateIndex(
                name: "ix_lawsuit_class_acronym",
                schema: "harvey",
                table: "lawsuit_class",
                column: "acronym");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "lawsuit_class",
                schema: "harvey");

            migrationBuilder.DropTable(
                name: "lawsuit_topic",
                schema: "harvey");

            migrationBuilder.DeleteData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "STM");

            migrationBuilder.DeleteData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "TSE");

            migrationBuilder.DeleteData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "TST");

            migrationBuilder.UpdateData(
                schema: "harvey",
                table: "legal_instance",
                keyColumn: "id",
                keyValue: "Other",
                column: "order",
                value: 5);
        }
    }
}
