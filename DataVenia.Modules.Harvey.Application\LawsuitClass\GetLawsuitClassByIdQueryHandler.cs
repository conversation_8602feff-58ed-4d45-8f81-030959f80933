﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Harvey.Domain.LawsuitClass;
using FluentResults;

namespace DataVenia.Modules.Harvey.Application.LawsuitClass;

internal sealed class GetLawsuitClassByIdQueryHandler(
    ILawsuitClassRepository lawsuitClassRepository) : IQueryHandlerFr<GetLawsuitClassByIdQuery, GetLawsuitClassByIdResponse>
{
    public async Task<Result<GetLawsuitClassByIdResponse>> Handle(GetLawsuitClassByIdQuery request, CancellationToken cancellationToken)
    {
        Domain.LawsuitClass.LawsuitClass? lawsuitClass = await lawsuitClassRepository.GetByIdAsync(request.id, cancellationToken);

        if (lawsuitClass is null)
            return Result.Fail<GetLawsuitClassByIdResponse>(new Error("LawsuitClass.Not.Found").WithMetadata("StatusCode", 404));

        var response = new GetLawsuitClassByIdResponse(
            lawsuitClass.Id,
            lawsuitClass.Type,
            lawsuitClass.LegalProvision,
            lawsuitClass.Article,
            lawsuitClass.Acronym,
            lawsuitClass.OldAcronym,
            lawsuitClass.ActivePole,
            lawsuitClass.PassivePole,
            lawsuitClass.Glossary,
            lawsuitClass.IsOwnNumbering,
            lawsuitClass.IsFirstInstance,
            lawsuitClass.IsSecondInstance,
            lawsuitClass.JustEsJuizadoEs,
            lawsuitClass.JustEsTurmas,
            lawsuitClass.JustEs1grauMil,
            lawsuitClass.JustEs2grauMil,
            lawsuitClass.JustEsJuizadoEsFp,
            lawsuitClass.JustTuEsUn,
            lawsuitClass.JustFed1grau,
            lawsuitClass.JustFed2grau,
            lawsuitClass.JustFedJuizadoEs,
            lawsuitClass.JustFedTurmas,
            lawsuitClass.JustFedNacional,
            lawsuitClass.JustFedRegional,
            lawsuitClass.JustTrab1grau,
            lawsuitClass.JustTrab2grau,
            lawsuitClass.JustTrabTst,
            lawsuitClass.JustTrabCsjt,
            lawsuitClass.Stf,
            lawsuitClass.Stj,
            lawsuitClass.Cjf,
            lawsuitClass.Cnj,
            lawsuitClass.JustMilUniao1grau,
            lawsuitClass.JustMilUniaoStm,
            lawsuitClass.JustMilEst1grau,
            lawsuitClass.JustMilEstTjm,
            lawsuitClass.JustElei1grau,
            lawsuitClass.JustElei2grau,
            lawsuitClass.JustEleiTse,
            lawsuitClass.ItemType,
            lawsuitClass.IncludedBy,
            lawsuitClass.IncludedAt,
            lawsuitClass.IsCriminal);

        return response;
    }
}
