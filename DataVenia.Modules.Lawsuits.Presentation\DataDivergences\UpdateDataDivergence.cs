using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.DataDivergences.UpdateDataDivergence;
using DataVenia.Modules.Users.Presentation;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace DataVenia.Modules.Lawsuits.Presentation.DataDivergences;

public sealed class UpdateDataDivergence(ILogger<UpdateDataDivergence> logger) : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPatch("/offices/{officeId}/divergences/{divergenceId}", async (
            Guid officeId,
            Guid divergenceId,
            [FromBody] UpdateDataDivergenceRequest request,
            ClaimsPrincipal claims,
            [FromServices] ISender sender) =>
        {
            try
            {
                // Get the user ID from claims for the AnalyzedBy field
                var userId = claims.GetUserId();

                Result result = await sender.Send(new UpdateDataDivergenceCommand(
                    divergenceId,
                    request.WasAccepted,
                    request.Reason,
                    userId.ToString()));

                return result.ToHttpResult();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error updating data divergence {DivergenceId}", divergenceId);
                return Results.Problem("An error occurred while updating the data divergence");
            }
        })
        .RequireAuthorization("office:lawsuits:write")
        .WithTags(Tags.Lawsuits);
    }
}

public sealed class UpdateDataDivergenceRequest
{
    public bool WasAccepted { get; init; }
    public string Reason { get; init; } = string.Empty;
}
