using FluentResults;

namespace DataVenia.Modules.AssociationHub.Domain.Services;

public interface IEntityHttpService
{
    Task<Result<EntityData>> GetEntityAsync(string entityType, Guid entityId, CancellationToken cancellationToken = default);
    Task<Result> UpdateEntityAssociationsAsync(string entityType, Guid entityId, Dictionary<string, List<Guid>> associations, CancellationToken cancellationToken = default);
}

public sealed class EntityData
{
    public Guid Id { get; init; }
    public Dictionary<string, List<Guid>> Associations { get; init; } = new();
    public Dictionary<string, object> AdditionalData { get; init; } = new();
}
