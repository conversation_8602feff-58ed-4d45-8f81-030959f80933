using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.AssociationHub.Domain.EntityConfiguration;
using FluentResults;

namespace DataVenia.Modules.AssociationHub.Application.Associations.ValidateAssociations;

internal sealed class ValidateAssociationsQueryHandler(
    EntityConfigurationRegistry configurationRegistry) : IQueryHandlerFr<ValidateAssociationsQuery, AssociationValidationResponse>
{
    public Task<Result<AssociationValidationResponse>> <PERSON>le(ValidateAssociationsQuery request, CancellationToken cancellationToken)
    {
        if (!configurationRegistry.IsEntitySupported(request.EntityType))
        {
            return Task.FromResult(Result.Fail<AssociationValidationResponse>(
                new Error($"Entity type '{request.EntityType}' is not supported for associations")
                    .WithMetadata("StatusCode", 404)));
        }

        var validationResult = configurationRegistry.ValidateAssociations(request.EntityType, request.TargetEntityTypes);

        var response = new AssociationValidationResponse(
            request.EntityType,
            validationResult.ValidTargets,
            validationResult.InvalidTargets,
            validationResult.UnsupportedTargets,
            validationResult.IsValid);

        return Task.FromResult(Result.Ok(response));
    }
}
