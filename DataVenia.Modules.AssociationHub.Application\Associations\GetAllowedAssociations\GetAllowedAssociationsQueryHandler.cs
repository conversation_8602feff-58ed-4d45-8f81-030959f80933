using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.AssociationHub.Domain.EntityConfiguration;
using FluentResults;

namespace DataVenia.Modules.AssociationHub.Application.Associations.GetAllowedAssociations;

internal sealed class GetAllowedAssociationsQueryHandler(
    EntityConfigurationRegistry configurationRegistry) : IQueryHandlerFr<GetAllowedAssociationsQuery, AllowedAssociationsResponse>
{
    public Task<Result<AllowedAssociationsResponse>> <PERSON>le(GetAllowedAssociationsQuery request, CancellationToken cancellationToken)
    {
        var configuration = configurationRegistry.GetConfiguration(request.EntityType);
        
        if (configuration == null || !configuration.IsEnabled)
        {
            return Task.FromResult(Result.Fail<AllowedAssociationsResponse>(
                new Error($"Entity type '{request.EntityType}' is not supported for associations")
                    .WithMetadata("StatusCode", 404)));
        }

        var allowedAssociations = configurationRegistry.GetAllowedAssociations(request.EntityType);
        var forbiddenAssociations = configurationRegistry.GetForbiddenAssociations(request.EntityType);

        var response = new AllowedAssociationsResponse(
            request.EntityType,
            allowedAssociations,
            forbiddenAssociations,
            configuration.AssociationMode.ToString());

        return Task.FromResult(Result.Ok(response));
    }
}
