using DataVenia.Modules.AssociationHub.Application.Abstractions.Data;
using DataVenia.Modules.AssociationHub.Domain.AssociationRequest;
using DataVenia.Modules.AssociationHub.Infrastructure.AssociationRequest;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.AssociationHub.Infrastructure.Database;

public sealed class AssociationHubDbContext(DbContextOptions<AssociationHubDbContext> options) : DbContext(options), IUnitOfWork
{
    internal DbSet<Domain.AssociationRequest.AssociationRequest> AssociationRequests { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema(Schemas.AssociationHub);

        modelBuilder.ApplyConfiguration(new AssociationRequestConfiguration());

        base.OnModelCreating(modelBuilder);
    }
}
