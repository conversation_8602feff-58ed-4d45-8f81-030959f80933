using DataVenia.Common.Application.Messaging;
using FluentResults;

namespace DataVenia.Modules.Lawsuits.Application.DataDivergences.GetDataDivergences;

internal sealed class GetDataDivergencesQueryHandler(IDataDivergenceRepository dataDivergenceRepository)
    : IQueryHandlerFr<GetDataDivergencesQuery, IReadOnlyCollection<DataDivergenceResponse>>
{
    public async Task<Result<IReadOnlyCollection<DataDivergenceResponse>>> Handle(
        GetDataDivergencesQuery request, 
        CancellationToken cancellationToken)
    {
        Result<IReadOnlyCollection<Domain.Lawsuits.DataDivergence>> result = 
            await dataDivergenceRepository.GetAllByOfficeIdAsync(request.OfficeId, cancellationToken);

        if (result.IsFailed)
            return Result.Fail<IReadOnlyCollection<DataDivergenceResponse>>(result.Errors);

        var responses = result.Value
            .Select(DataDivergenceResponse.FromDomain)
            .ToList();

        return Result.Ok<IReadOnlyCollection<DataDivergenceResponse>>(responses);
    }
}
