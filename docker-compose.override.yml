version: "3.8"

services:
  datavenia.api:
    image: mcr.microsoft.com/dotnet/aspnet:9.0
    volumes:
      - ./DataVenia.Api/bin/Debug/net9.0/publish:/app
    working_dir: /app
    command: dotnet DataVenia.Api.dll
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_HTTP_PORTS=8080
      - ConnectionStrings__Default=Host=datavenia.database;Port=5432;Database=datavenia;Username=********;Password=********
