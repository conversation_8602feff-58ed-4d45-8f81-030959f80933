using DataVenia.Modules.AssociationHub.Domain.AssociationRequest;
using DataVenia.Modules.AssociationHub.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.AssociationHub.Infrastructure.AssociationRequest;

internal sealed class AssociationRequestRepository(AssociationHubDbContext context) : IAssociationRequestRepository
{
    public async Task<Domain.AssociationRequest.AssociationRequest?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await context.AssociationRequests
            .FirstOrDefaultAsync(ar => ar.Id == id, cancellationToken);
    }

    public async Task<IReadOnlyCollection<Domain.AssociationRequest.AssociationRequest>> GetPendingRequestsAsync(CancellationToken cancellationToken = default)
    {
        return await context.AssociationRequests
            .Where(ar => ar.Status == AssociationRequestStatus.Pending)
            .OrderBy(ar => ar.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyCollection<Domain.AssociationRequest.AssociationRequest>> GetFailedRequestsForRetryAsync(int maxRetries = 3, CancellationToken cancellationToken = default)
    {
        return await context.AssociationRequests
            .Where(ar => ar.Status == AssociationRequestStatus.Failed && ar.RetryCount < maxRetries)
            .OrderBy(ar => ar.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(Domain.AssociationRequest.AssociationRequest request, CancellationToken cancellationToken = default)
    {
        await context.AssociationRequests.AddAsync(request, cancellationToken);
    }

    public Task UpdateAsync(Domain.AssociationRequest.AssociationRequest request, CancellationToken cancellationToken = default)
    {
        context.AssociationRequests.Update(request);
        return Task.CompletedTask;
    }

    public async Task<bool> ExistsAsync(string entityType, Guid entityId, string targetEntityType, Guid targetEntityId, AssociationOperation operation, CancellationToken cancellationToken = default)
    {
        return await context.AssociationRequests
            .AnyAsync(ar => 
                ar.EntityType == entityType &&
                ar.EntityId == entityId &&
                ar.TargetEntityType == targetEntityType &&
                ar.TargetEntityId == targetEntityId &&
                ar.Operation == operation &&
                ar.Status != AssociationRequestStatus.Failed,
                cancellationToken);
    }
}
