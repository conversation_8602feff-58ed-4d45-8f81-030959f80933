using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.AssociationHub.Application.Associations.GetAllowedAssociations;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Mvc;

namespace DataVenia.Modules.AssociationHub.Presentation.Associations;

internal sealed class GetAllowedAssociations : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("/associations/allowed/{entityType}", async (string entityType, [FromServices] ISender sender) =>
        {
            var result = await sender.Send(new GetAllowedAssociationsQuery(entityType));

            return result.Match(Results.Ok, ApiResults.Problem);
        })
        .RequireAuthorization("system:associations:read")
        .WithTags(Tags.Associations)
        .WithSummary("Get allowed associations for an entity type")
        .WithDescription("Returns the list of entity types that can be associated with the specified entity type");
    }
}
