namespace DataVenia.Modules.AssociationHub.Domain.AssociationRequest;

public interface IAssociationRequestRepository
{
    Task<AssociationRequest?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IReadOnlyCollection<AssociationRequest>> GetPendingRequestsAsync(CancellationToken cancellationToken = default);
    Task<IReadOnlyCollection<AssociationRequest>> GetFailedRequestsForRetryAsync(int maxRetries = 3, CancellationToken cancellationToken = default);
    Task AddAsync(AssociationRequest request, CancellationToken cancellationToken = default);
    Task UpdateAsync(AssociationRequest request, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string entityType, Guid entityId, string targetEntityType, Guid targetEntityId, AssociationOperation operation, CancellationToken cancellationToken = default);
}
