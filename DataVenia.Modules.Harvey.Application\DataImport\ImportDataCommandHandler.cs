﻿using DataVenia.Common.Application.Messaging;
using FluentResults;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Harvey.Application.DataImport;

internal sealed class ImportDataCommandHandler(
    DataImportService dataImportService,
    ILogger<ImportDataCommandHandler> logger) : ICommandHandlerFr<ImportDataCommand, ImportDataResponse>
{
    public async Task<Result<ImportDataResponse>> <PERSON><PERSON>(ImportDataCommand request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Starting data import process");

            // Define the paths to the JSON files
            string basePath = AppDomain.CurrentDomain.BaseDirectory;
            string lawsuitClassesPath = Path.Combine(basePath, "Database", "Data", "lawsuitClasses.json");
            string lawsuitTopicsPath = Path.Combine(basePath, "Database", "Data", "lawsuitTopics.json");

            // If files don't exist in the output directory, try to find them in the project directory
            if (!File.Exists(lawsuitClassesPath) || !File.Exists(lawsuitTopicsPath))
            {
                // Try to find the files in the project directory (for development environment)
                string projectBasePath = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "DataVenia.Modules.Harvey.Infrastructure", "Database", "Data"));

                if (File.Exists(Path.Combine(projectBasePath, "lawsuitClasses.json")))
                {
                    lawsuitClassesPath = Path.Combine(projectBasePath, "lawsuitClasses.json");
                }

                if (File.Exists(Path.Combine(projectBasePath, "lawsuitTopics.json")))
                {
                    lawsuitTopicsPath = Path.Combine(projectBasePath, "lawsuitTopics.json");
                }
            }

            logger.LogInformation("Using base path: {BasePath}", basePath);
            logger.LogInformation("Lawsuit classes path: {Path}", lawsuitClassesPath);
            logger.LogInformation("Lawsuit topics path: {Path}", lawsuitTopicsPath);

            // Import lawsuit classes
            ImportResult lawsuitClassesResult = await dataImportService.ImportLawsuitClassesAsync(lawsuitClassesPath, cancellationToken);
            logger.LogInformation("Lawsuit classes import result: {Result}", lawsuitClassesResult.Message);

            // Import lawsuit topics
            ImportResult lawsuitTopicsResult = await dataImportService.ImportLawsuitTopicsAsync(lawsuitTopicsPath, cancellationToken);
            logger.LogInformation("Lawsuit topics import result: {Result}", lawsuitTopicsResult.Message);

            bool success = lawsuitClassesResult.Success || lawsuitTopicsResult.Success;
            string message = success
                ? "Data import completed successfully"
                : "Data import failed";

            return new ImportDataResponse(
                success,
                message,
                lawsuitClassesResult,
                lawsuitTopicsResult);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during data import");
            return Result.Fail<ImportDataResponse>(new Error( $"Error during data import: {ex.Message}").WithMetadata("StatusCode", 500));
        }
    }
}
