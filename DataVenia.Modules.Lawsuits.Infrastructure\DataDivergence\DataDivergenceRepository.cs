using DataVenia.Modules.Lawsuits.Application.DataDivergences;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using FluentResults;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Infrastructure.DataDivergence;

internal sealed class DataDivergenceRepository(LawsuitsDbContext context, ILogger<DataDivergenceRepository> logger): IDataDivergenceRepository
{
    public void Insert(Domain.Lawsuits.DataDivergence dataDivergence)
    {
        context.DataDivergences.Add(dataDivergence);
    }

    public async Task<Result<IReadOnlyCollection<Domain.Lawsuits.DataDivergence>>> GetByLawsuitIdAsync(Guid officeId, CancellationToken cancellationToken = default)
    {
        try
        {
            var dataDivergences = await context.DataDivergences
                .Where(dd => dd.OfficeId == officeId)
                .OrderByDescending(dd => dd.CreatedAt)
                .ToListAsync(cancellationToken);

            return Result.Ok<IReadOnlyCollection<Domain.Lawsuits.DataDivergence>>(dataDivergences);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting data divergences for office {OfficeId}", officeId);
            return Result.Fail<IReadOnlyCollection<Domain.Lawsuits.DataDivergence>>("Failed to retrieve data divergences");
        }
    }
}
