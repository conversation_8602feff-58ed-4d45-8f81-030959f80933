﻿using DataVenia.Modules.Lawsuits.Application.DataDivergences;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Infrastructure.DataDivergence;

internal sealed class DataDivergenceRepository(LawsuitsDbContext context, ILogger<DataDivergenceRepository> logger): IDataDivergenceRepository
{
    public void Insert(Domain.Lawsuits.DataDivergence dataDivergence)
    {
        context.DataDivergences.Add(dataDivergence);
    }
}
