using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.AssociationHub.Application.Abstractions.Data;
using DataVenia.Modules.AssociationHub.Domain.AssociationRequest;
using DataVenia.Modules.AssociationHub.Domain.EntityConfiguration;
using FluentResults;

namespace DataVenia.Modules.AssociationHub.Application.Associations.CreateAssociation;

internal sealed class CreateAssociationCommandHandler(
    IAssociationRequestRepository associationRequestRepository,
    EntityConfigurationRegistry configurationRegistry,
    IUnitOfWork unitOfWork) : ICommandHandlerFr<CreateAssociationCommand>
{
    public async Task<Result> Handle(CreateAssociationCommand request, CancellationToken cancellationToken)
    {
        // Validate that the source entity type is supported
        if (!configurationRegistry.IsEntitySupported(request.EntityType))
        {
            return Result.Fail(new Error($"Entity type '{request.EntityType}' is not supported for associations")
                .WithMetadata("StatusCode", 400));
        }

        // Validate all target entity types and associations
        var allTargetTypes = request.Associations.Select(a => a.TargetEntityType).Distinct().ToList();
        var validationResult = configurationRegistry.ValidateAssociations(request.EntityType, allTargetTypes);

        if (validationResult.HasErrors)
        {
            var errors = new List<string>();

            if (validationResult.UnsupportedTargets.Any())
            {
                errors.Add($"Unsupported entity types: {string.Join(", ", validationResult.UnsupportedTargets)}");
            }

            if (validationResult.InvalidTargets.Any())
            {
                errors.Add($"Invalid associations for '{request.EntityType}': {string.Join(", ", validationResult.InvalidTargets)}");
            }

            return Result.Fail(new Error(string.Join("; ", errors))
                .WithMetadata("StatusCode", 400));
        }

        var associationRequests = new List<AssociationRequest>();

        // Create association requests only for valid target entities
        foreach (var association in request.Associations)
        {
            // Skip invalid associations (already validated above)
            if (!validationResult.ValidTargets.Contains(association.TargetEntityType))
            {
                continue;
            }

            foreach (var targetEntityId in association.TargetEntityIds)
            {
                // Check if association request already exists
                bool exists = await associationRequestRepository.ExistsAsync(
                    request.EntityType,
                    request.EntityId,
                    association.TargetEntityType,
                    targetEntityId,
                    AssociationOperation.Add,
                    cancellationToken);

                if (exists)
                {
                    continue; // Skip existing associations
                }

                // Validate bidirectional association (target can associate back to source)
                if (!configurationRegistry.CanAssociate(association.TargetEntityType, request.EntityType))
                {
                    // Only create unidirectional association if target can't associate back
                    var sourceToTargetRequest = AssociationRequest.Create(
                        request.EntityType,
                        request.EntityId,
                        association.TargetEntityType,
                        targetEntityId,
                        AssociationOperation.Add);

                    associationRequests.Add(sourceToTargetRequest);
                }
                else
                {
                    // Create bidirectional association requests
                    var sourceToTargetRequest = AssociationRequest.Create(
                        request.EntityType,
                        request.EntityId,
                        association.TargetEntityType,
                        targetEntityId,
                        AssociationOperation.Add);

                    var targetToSourceRequest = AssociationRequest.Create(
                        association.TargetEntityType,
                        targetEntityId,
                        request.EntityType,
                        request.EntityId,
                        AssociationOperation.Add);

                    associationRequests.Add(sourceToTargetRequest);
                    associationRequests.Add(targetToSourceRequest);
                }
            }
        }

        // Add all association requests in batch
        foreach (var associationRequest in associationRequests)
        {
            await associationRequestRepository.AddAsync(associationRequest, cancellationToken);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}
