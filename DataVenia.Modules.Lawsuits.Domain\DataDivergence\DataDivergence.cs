using System.Text.Json;
using DataVenia.Common.Domain;

namespace DataVenia.Modules.Lawsuits.Domain.DataDivergence;

public sealed class DataDivergence : Entity
{
    private readonly Dictionary<string, DivergentField> _fields = new();

    public Guid Id { get; private set; }
    public Guid LawsuitId { get; private set; }
    public Guid OfficeId { get; private set; }
    public string InstanceId { get; private set; }
    public string FieldsJson { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public DateTime? AnalyzedAt { get; private set; }
    public string? AnalyzedBy { get; private set; }
    public DataDivergenceStatus Status { get; private set; }
    public string? Reason { get; private set; }

    public IReadOnlyDictionary<string, DivergentField> Fields
    {
        get
        {
            if (_fields.Count == 0 && !string.IsNullOrEmpty(FieldsJson))
            {
                var deserializedFields = JsonSerializer.Deserialize<Dictionary<string, DivergentField>>(<PERSON><PERSON><PERSON>);
                if (deserializedFields != null)
                {
                    foreach (var field in deserializedFields)
                    {
                        _fields[field.Key] = field.Value;
                    }
                }
            }
            return _fields.AsReadOnly();
        }
    }

    private DataDivergence() { }

    public static DataDivergence Create(Guid lawsuitId, Guid officeId, string instanceId, Dictionary<string, DivergentField> fields)
    {
        var divergence = new DataDivergence
        {
            Id = Guid.CreateVersion7(),
            LawsuitId = lawsuitId,
            OfficeId = officeId,
            InstanceId = instanceId,
            Status = DataDivergenceStatus.Pending,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = null,
            AnalyzedAt = null,
            AnalyzedBy = null,
        };

        foreach (var field in fields)
        {
            divergence._fields[field.Key] = field.Value;
        }

        divergence.FieldsJson = JsonSerializer.Serialize(divergence._fields);
        return divergence;
    }

    public void MarkAsAnalyzed(string analyzedBy, bool wasAccepted, string reason)
    {
        AnalyzedAt = DateTime.UtcNow;
        AnalyzedBy = analyzedBy;
        Status = wasAccepted ? DataDivergenceStatus.Accepted : DataDivergenceStatus.Denied;
        Reason = reason;
    }

    public void MarkAsOverwritten()
    {
        Status = DataDivergenceStatus.Overwritten;
    }
    
    public void UpdateLastSeenTime()
    {
        UpdatedAt = DateTime.UtcNow;
    }

    public bool HasAnyFieldWithDifferentSuggestedValue(Dictionary<string, string> suggestedValues)
    {
        foreach (var newValue in suggestedValues)
        {
            if (!HasSameSuggestedValue(newValue.Key, newValue.Value))
            {
                return true;
            }
        }
        return false;
    }
    
    private bool HasSameSuggestedValue(string fieldName, string suggestedValue)
    {
        return Fields.TryGetValue(fieldName, out var field) && field.SuggestedValue == suggestedValue;
    }
}



public sealed record DivergentField(string CurrentValue, string SuggestedValue);
