﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\DataVenia.Common.Infrastructure\DataVenia.Common.Infrastructure.csproj" />
    <ProjectReference Include="..\DataVenia.Common.Presentation\DataVenia.Common.Presentation.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.Harvey.Domain\DataVenia.Modules.Harvey.Domain.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.Harvey.Presentation\DataVenia.Modules.Harvey.Presentation.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="CourtDivision\" />
    <Folder Include="Forum\" />
    <Folder Include="LegalInstance\" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Database\Data\lawsuitClasses.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Database\Data\lawsuitTopics.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
