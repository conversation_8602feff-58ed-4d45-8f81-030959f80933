﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Harvey.Domain.LawsuitTopic;
using FluentResults;

namespace DataVenia.Modules.Harvey.Application.LawsuitTopic;

internal sealed class GetLawsuitTopicsQueryHandler(
    ILawsuitTopicRepository lawsuitTopicRepository) : IQueryHandlerFr<GetLawsuitTopicsQuery, IReadOnlyCollection<GetLawsuitTopicsResponse>>
{
    public async Task<Result<IReadOnlyCollection<GetLawsuitTopicsResponse>>> Handle(GetLawsuitTopicsQuery request, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<Domain.LawsuitTopic.LawsuitTopic> topics = await lawsuitTopicRepository.GetAllAsync(cancellationToken);

        var topicsResponse = topics.Select(t => new GetLawsuitTopicsResponse(
            t.Id,
            t.LegalProvision,
            t.Article,
            t.Glossary,
            t.<PERSON>,
            t.<PERSON>,
            t.<PERSON>,
            t.<PERSON>,
            t.IsSecondInstance)).ToList();

        return topicsResponse;
    }
}
