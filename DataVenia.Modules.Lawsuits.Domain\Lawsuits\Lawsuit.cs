﻿using System.Globalization;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using DataVenia.Modules.Lawsuits.Domain.LawsuitSteps;
using Result = FluentResults.Result;

namespace DataVenia.Modules.Lawsuits.Domain.Lawsuits;

public sealed class Lawsuit : Entity
{
    private readonly List<LawsuitData> _lawsuitDatas = [];
    private readonly List<MonitoringHistoryEntry> _monitoringHistory = [];
    private readonly List<LawsuitStep> _lawsuitSteps = [];
    private readonly List<DataDivergence> _dataDivergences = [];
    private Lawsuit() { }

    public Guid Id { get; private set; }
    public Guid OfficeId { get; private set; }
    public string Cnj { get; private set; }
    public string? LawsuitTypeId { get; private set; } // Natureza
    public string? ClassId { get; private set; }
    public DateTime? DistributedAt { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public bool MonitoringEnabled { get; private set; }
    public bool IsFirstTimeSyncCompleted { get; private set; }
    public IReadOnlyCollection<MonitoringHistoryEntry> MonitoringHistory => _monitoringHistory.AsReadOnly();
    public IReadOnlyCollection<LawsuitData> LawsuitDatas => _lawsuitDatas.AsReadOnly();
    public ICollection<LawsuitStep> LawsuitSteps => _lawsuitSteps.AsReadOnly();
    public IReadOnlyCollection<DataDivergence> DataDivergences => _dataDivergences.AsReadOnly();

    public static Lawsuit Create(
        string cnj,
        Guid officeId,
        string lawsuitTypeId,
        string classId,
        DateTime distributedAt
    )
    {
        var lawsuit = new Lawsuit
        {
            Id = Guid.CreateVersion7(),
            OfficeId = officeId,
            Cnj = cnj,
            LawsuitTypeId = lawsuitTypeId,
            ClassId = classId,
            CreatedAt = DateTime.UtcNow,
            DistributedAt = distributedAt,
            MonitoringEnabled = false,
            IsFirstTimeSyncCompleted = false,
        };

        return lawsuit;
    }

    public static Lawsuit CreateByCnj(string cnj, Guid officeId, string? lawsuitTypeId, Guid userId)
    {
        var lawsuit = new Lawsuit
        {
            Id = Guid.CreateVersion7(),
            OfficeId = officeId,
            Cnj = cnj,
            LawsuitTypeId = lawsuitTypeId,
            CreatedAt = DateTime.UtcNow,
            MonitoringEnabled = false,
            IsFirstTimeSyncCompleted = false
        };

        return lawsuit;
    }

    /// <summary>
    /// Activates monitoring. Validates that any previous monitoring period is closed.
    /// </summary>
    public Result ActivateMonitoring(Guid userId)
    {
        // If there is an existing monitoring history and its last entry is not closed, return an error.
        if (_monitoringHistory.Any() && _monitoringHistory[^1].StoppedAt == null)
            return Result.Fail(new FluentResults.Error("Monitoring.AlreadyActive").WithMetadata("StatusCode", 400));

        MonitoringEnabled = true;
        _monitoringHistory.Add(new MonitoringHistoryEntry(DateTime.UtcNow, userId));
        return Result.Ok();
    }

    /// <summary>
    /// Deactivates monitoring. Validates that the last monitoring period is still active.
    /// </summary>
    public Result DeactivateMonitoring(Guid userId)
    {
        // If there is no monitoring history or the last entry is already closed, return an error.
        if (!_monitoringHistory.Any() || _monitoringHistory[^1].StoppedAt != null)
            return Result.Fail(new FluentResults.Error("Monitoring.NotActive").WithMetadata("StatusCode", 400));

        MonitoringEnabled = false;
        _monitoringHistory[^1].SetStoppedAt(DateTime.UtcNow, userId);
        return Result.Ok();
    }

    private FluentResults.Result<DataDivergence> AddOrUpdateDataDivergence(DataDivergence divergentFields)
    {
        if (divergentFields.Fields.Count == 0)
            return Result.Fail(new FluentResults.Error("DataDivergence.NotNeeded").WithMetadata("StatusCode", 204));

        // Find existing divergence for this instance
        var existingDivergence = _dataDivergences
            .Where(d => d.InstanceId == divergentFields.InstanceId && !d.AnalyzedAt.HasValue)
            .OrderByDescending(d => d.CreatedAt)
            .FirstOrDefault();

        if (existingDivergence != null)
        {
            if (!existingDivergence.HasAnyFieldWithDifferentSuggestedValue(
                    divergentFields.Fields.ToDictionary(f => f.Key, f => f.Value.SuggestedValue)))
            {
                existingDivergence.MarkAsOverwritten();

                return DataDivergence.Create(Id, OfficeId, divergentFields.InstanceId,
                    divergentFields.Fields.ToDictionary(f => f.Key, f => f.Value));
            }

            // TODO: Test if this is being saved in the database
            existingDivergence.UpdateLastSeenTime();
        }
        else
        {
            return DataDivergence.Create(Id, OfficeId, divergentFields.InstanceId,
                divergentFields.Fields.ToDictionary(f => f.Key, f => f.Value));
        }

        return Result.Fail<DataDivergence>(
            new FluentResults.Error("DataDivergence.Already.Reported").WithMetadata("StatusCode", 208));
    }

    public FluentResults.Result<DataDivergence> CheckAndUpdateCoverData(
        string instanceId,
        string? lawsuitTypeId,
        string? classId,
        DateTime? distributedAt,
        string? judgingOrganId = null,
        decimal? causeValue = null,
        List<string>? topicIds = null)
    {
        var divergentFields = new Dictionary<string, (string current, string suggestedValue)>();

        CheckLawsuitTypeId(lawsuitTypeId, divergentFields);
        CheckClassId(classId, divergentFields);
        CheckDistributedAt(distributedAt, divergentFields);
    
        var latestLawsuitData = _lawsuitDatas
            .OrderByDescending(d => d.CreatedAt)
            .FirstOrDefault(x => x.LegalInstanceId == instanceId);

        if (latestLawsuitData != null)
        {
            CheckJudgingOrganId(judgingOrganId, latestLawsuitData, divergentFields);
            CheckCauseValue(causeValue, latestLawsuitData, divergentFields);
            CheckTopicIds(topicIds, latestLawsuitData, divergentFields);
        }

        if (divergentFields.Any())
        {
            var parsedDivergentFields = DataDivergence.Create(
                Id,
                OfficeId,
                instanceId,
                divergentFields.ToDictionary(f => f.Key, f => new DivergentField(f.Value.current, f.Value.suggestedValue))
            );

            return AddOrUpdateDataDivergence(parsedDivergentFields);
        }

        return Result.Fail(new FluentResults.Error("DataDivergence.NotNeeded").WithMetadata("StatusCode", 204));
    }


    public void SetFirstTimeSyncCompleted()
    {
        IsFirstTimeSyncCompleted = true;
    }

    public void MarkDataDivergenceAsAnalyzed(Guid divergenceId, string analyzedBy, bool wasAccepted, string reason)
    {
        var divergence = _dataDivergences.FirstOrDefault(d => d.Id == divergenceId);
        if (divergence != null)
            divergence.MarkAsAnalyzed(analyzedBy, wasAccepted, reason);
    }

    private void CheckLawsuitTypeId(string? lawsuitTypeId,
        Dictionary<string, (string current, string suggestedValue)> fields)
    {
        if (!string.Equals(LawsuitTypeId, lawsuitTypeId))
        {
            if (string.IsNullOrEmpty(LawsuitTypeId))
                LawsuitTypeId = lawsuitTypeId;
            else
                fields["LawsuitTypeId"] = (LawsuitTypeId ?? string.Empty, lawsuitTypeId ?? string.Empty);
        }
    }

    private void CheckClassId(string? classId, Dictionary<string, (string current, string suggestedValue)> fields)
    {
        if (!string.Equals(ClassId, classId))
        {
            if (string.IsNullOrEmpty(ClassId))
                ClassId = classId;
            else
                fields["ClassId"] = (ClassId ?? string.Empty, classId ?? string.Empty);
        }
    }

    private void CheckDistributedAt(DateTime? distributedAt,
        Dictionary<string, (string current, string suggestedValue)> fields)
    {
        var isValid = distributedAt.HasValue && distributedAt.Value.Year > 1;

        if (!isValid)
            return;

        bool isCurrentEmpty = !DistributedAt.HasValue || DistributedAt.Value.Year <= 1;

        if (isCurrentEmpty)
            DistributedAt = distributedAt;
        else if (DistributedAt != distributedAt)
            fields["DistributedAt"] = (
                DistributedAt?.ToString("yyyy-MM-dd") ?? string.Empty,
                distributedAt.Value.ToString("yyyy-MM-dd")
            );
    }

    private void CheckJudgingOrganId(string? judgingOrganId, LawsuitData latest,
        Dictionary<string, (string current, string suggestedValue)> fields)
    {
        if (!string.IsNullOrEmpty(judgingOrganId) && !string.IsNullOrEmpty(latest.JudgingOrganId) &&
            !string.Equals(latest.JudgingOrganId, judgingOrganId))
        {
            fields["JudgingOrganId"] = (latest.JudgingOrganId, judgingOrganId);
        }
    }

    private void CheckCauseValue(decimal? causeValue, LawsuitData latest,
        Dictionary<string, (string current, string suggestedValue)> fields)
    {
        if (causeValue.HasValue && causeValue.Value > 0 &&
            latest.CauseValue.HasValue && latest.CauseValue.Value > 0 &&
            latest.CauseValue != causeValue)
        {
            fields["CauseValue"] = (
                latest.CauseValue?.ToString() ?? string.Empty,
                causeValue.Value.ToString(CultureInfo.InvariantCulture)
            );
        }
    }

    private void CheckTopicIds(List<string>? topicIds, LawsuitData latest,
        Dictionary<string, (string current, string suggestedValue)> fields)
    {
        if (topicIds == null || !topicIds.Any())
            return;

        if (!latest.TopicIds.Any())
        {
            fields["TopicIds"] = (string.Empty, string.Join(",", topicIds));
            return;
        }

        var numericTopicIds = topicIds
            .Select(id => int.TryParse(id, out var num) ? (int?)num : null)
            .Where(id => id.HasValue)
            .Select(id => id.Value)
            .ToList();

        if (!numericTopicIds.Any())
            return;

        var currentSet = latest.TopicIds.ToHashSet();
        var eventSet = numericTopicIds.ToHashSet();

        var missing = eventSet.Except(currentSet).ToList();

        if (missing.Any())
        {
            var currentStr = string.Join(", ", latest.TopicIds);
            var eventStr = string.Join(", ", numericTopicIds);
            fields["TopicIds"] = (currentStr, eventStr);
        }
    }
}
