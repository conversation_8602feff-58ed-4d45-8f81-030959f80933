using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Application.Lawsuits;
using DataVenia.Modules.Lawsuits.Domain.DataDivergence;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using FluentResults;
using Microsoft.Extensions.Logging;
using Result = FluentResults.Result;

namespace DataVenia.Modules.Lawsuits.Application.DataDivergences.UpdateDataDivergence;

internal sealed class UpdateDataDivergenceCommandHandler(
    IDataDivergenceRepository dataDivergenceRepository,
    ILawsuitRepository lawsuitRepository,
    ILawsuitDataRepository lawsuitDataRepository,
    IUnitOfWork unitOfWork,
    ILogger<UpdateDataDivergenceCommandHandler> logger) : ICommandHandlerFr<UpdateDataDivergenceCommand>
{
    public async Task<Result> Handle(UpdateDataDivergenceCommand request, CancellationToken cancellationToken)
    {
        // Get the data divergence
        var divergenceResult = await dataDivergenceRepository.GetByIdAsync(request.DivergenceId, cancellationToken);
        if (divergenceResult.IsFailed)
        {
            logger.LogError("Data divergence not found: {DivergenceId}", request.DivergenceId);
            return Result.Fail(new Error("DataDivergence.Not.Found").WithMetadata("StatusCode", 404));
        }

        var dataDivergence = divergenceResult.Value;

        // Check if already analyzed
        if (dataDivergence.Status != DataDivergenceStatus.Pending)
        {
            logger.LogWarning("Data divergence {DivergenceId} has already been analyzed", request.DivergenceId);
            return Result.Fail(new Error("DataDivergence.Already.Analyzed").WithMetadata("StatusCode", 400));
        }

        // Mark as analyzed
        dataDivergence.MarkAsAnalyzed(request.AnalyzedBy, request.WasAccepted, request.Reason);

        // If accepted, update the lawsuit and create new lawsuit data
        if (request.WasAccepted)
        {
            var updateResult = await UpdateLawsuitWithAcceptedChanges(dataDivergence, cancellationToken);
            if (updateResult.IsFailed)
            {
                return updateResult;
            }
        }

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
            logger.LogInformation("Data divergence {DivergenceId} updated successfully", request.DivergenceId);
            return Result.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating data divergence {DivergenceId}", request.DivergenceId);
            return Result.Fail(new Error("Internal.Server.Error").WithMetadata("StatusCode", 500));
        }
    }

    private async Task<Result> UpdateLawsuitWithAcceptedChanges(DataDivergence dataDivergence, CancellationToken cancellationToken)
    {
        // Get the lawsuit
        var lawsuit = await lawsuitRepository.GetLawsuitByIdAsync(dataDivergence.LawsuitId, cancellationToken);
        if (lawsuit == null)
        {
            logger.LogError("Lawsuit not found: {LawsuitId}", dataDivergence.LawsuitId);
            return Result.Fail(new Error("Lawsuit.Not.Found").WithMetadata("StatusCode", 404));
        }

        // Get the latest lawsuit data for the instance
        var latestLawsuitDataResult = await lawsuitDataRepository.GetLatestOfInstanceByLawsuitIdAsync(
            dataDivergence.LawsuitId, 
            dataDivergence.InstanceId, 
            cancellationToken);

        if (latestLawsuitDataResult.IsFailed)
        {
            logger.LogError("Latest lawsuit data not found for lawsuit {LawsuitId} and instance {InstanceId}", 
                dataDivergence.LawsuitId, dataDivergence.InstanceId);
            return Result.Fail(new Error("LawsuitData.Not.Found").WithMetadata("StatusCode", 404));
        }

        var latestLawsuitData = latestLawsuitDataResult.Value;

        // Apply the accepted changes to create new lawsuit data
        var newLawsuitDataResult = CreateUpdatedLawsuitData(latestLawsuitData, dataDivergence, lawsuit);
        if (newLawsuitDataResult.IsFailed)
        {
            return Result.Fail(newLawsuitDataResult.Errors);
        }

        // Insert the new lawsuit data
        lawsuitDataRepository.Insert(newLawsuitDataResult.Value);

        // Update lawsuit fields if they are lawsuit-level fields
        UpdateLawsuitFields(lawsuit, dataDivergence);
        lawsuitRepository.Update(lawsuit);

        return Result.Ok();
    }

    private FluentResults.Result<LawsuitData> CreateUpdatedLawsuitData(LawsuitData originalData, DataDivergence dataDivergence, Domain.Lawsuits.Lawsuit lawsuit)
    {
        // Start with the original data values
        var title = originalData.Title;
        var lawsuitStatusId = originalData.LawsuitStatusId;
        var topicIds = originalData.TopicIds.ToList();
        var judgingOrganId = originalData.JudgingOrganId;
        var causeValue = originalData.CauseValue;
        var convictionValue = originalData.ConvictionValue;
        var judgingOrganHref = originalData.JudgingOrganHref;
        var description = originalData.Description;
        var observations = originalData.Observations;
        var access = originalData.Access;

        // Apply the accepted changes
        foreach (var field in dataDivergence.Fields)
        {
            var fieldName = field.Key;
            var suggestedValue = field.Value.SuggestedValue;

            switch (fieldName.ToLowerInvariant())
            {
                case "title":
                    title = suggestedValue;
                    break;
                case "lawsuitstatusid":
                    lawsuitStatusId = suggestedValue;
                    break;
                case "judgingorganid":
                    judgingOrganId = suggestedValue;
                    break;
                case "causevalue":
                    if (decimal.TryParse(suggestedValue, out var parsedCauseValue))
                        causeValue = parsedCauseValue;
                    break;
                case "convictionvalue":
                    if (decimal.TryParse(suggestedValue, out var parsedConvictionValue))
                        convictionValue = parsedConvictionValue;
                    break;
                case "judgingorganhref":
                    judgingOrganHref = suggestedValue;
                    break;
                case "description":
                    description = suggestedValue;
                    break;
                case "observations":
                    observations = suggestedValue;
                    break;
                case "access":
                    access = suggestedValue;
                    break;
                case "topicids":
                    // Handle topic IDs - assuming they come as comma-separated string
                    if (!string.IsNullOrEmpty(suggestedValue))
                    {
                        var parsedTopicIds = suggestedValue.Split(',')
                            .Where(id => int.TryParse(id.Trim(), out _))
                            .Select(id => int.Parse(id.Trim()))
                            .ToList();
                        topicIds = parsedTopicIds;
                    }
                    break;
            }
        }

        // Get responsible IDs from the original data
        var responsibleIds = originalData.Responsibles.Select(r => r.LawyerId).ToList();

        // Create new lawsuit data
        return LawsuitData.Create(
            title,
            lawsuit.Cnj,
            lawsuit.Id,
            originalData.FolderId,
            originalData.LegalInstanceId,
            lawsuitStatusId,
            topicIds,
            judgingOrganId,
            causeValue,
            convictionValue,
            judgingOrganHref,
            description,
            observations,
            access,
            responsibleIds,
            originalData.EvolvedFromCaseId,
            originalData.GroupingCaseId,
            originalData.IsInstanceCreatedByUser);
    }

    private void UpdateLawsuitFields(Domain.Lawsuits.Lawsuit lawsuit, DataDivergence dataDivergence)
    {
        // Update lawsuit-level fields (these are fields that belong to the lawsuit entity itself)
        foreach (var field in dataDivergence.Fields)
        {
            var fieldName = field.Key;
            var suggestedValue = field.Value.SuggestedValue;

            switch (fieldName.ToLowerInvariant())
            {
                case "lawsuittypeid":
                    // Update lawsuit type - this would require a method on the lawsuit entity
                    // lawsuit.UpdateLawsuitType(suggestedValue);
                    break;
                case "classid":
                    // Update class - this would require a method on the lawsuit entity
                    // lawsuit.UpdateClass(suggestedValue);
                    break;
                case "distributedat":
                    if (DateTime.TryParse(suggestedValue, out var distributedAt))
                    {
                        // lawsuit.UpdateDistributedAt(distributedAt);
                    }
                    break;
            }
        }
    }
}
