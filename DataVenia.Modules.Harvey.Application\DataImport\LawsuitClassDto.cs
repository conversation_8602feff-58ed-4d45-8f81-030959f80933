﻿﻿namespace DataVenia.Modules.Harvey.Application.DataImport;

public class LawsuitClassDto
{
    public int Id { get; set; }
    public string? Type { get; set; }
    public string? LegalProvision { get; set; }
    public string? Article { get; set; }
    public string? Acronym { get; set; }
    public string? OldAcronym { get; set; }
    public string? ActivePole { get; set; }
    public string? PassivePole { get; set; }
    public string? Glossary { get; set; }
    public bool IsOwnNumbering { get; set; }
    public bool IsFirstInstance { get; set; }
    public bool IsSecondInstance { get; set; }
    public bool JustEsJuizadoEs { get; set; }
    public bool JustEsTurmas { get; set; }
    public bool JustEs1grauMil { get; set; }
    public bool JustEs2grauMil { get; set; }
    public bool JustEsJuizadoEsFp { get; set; }
    public bool JustTuEsUn { get; set; }
    public bool JustFed1grau { get; set; }
    public bool JustFed2grau { get; set; }
    public bool JustFedJuizadoEs { get; set; }
    public bool JustFedTurmas { get; set; }
    public bool JustFedNacional { get; set; }
    public bool JustFedRegional { get; set; }
    public bool JustTrab1grau { get; set; }
    public bool JustTrab2grau { get; set; }
    public bool JustTrabTst { get; set; }
    public bool JustTrabCsjt { get; set; }
    public bool Stf { get; set; }
    public bool Stj { get; set; }
    public bool Cjf { get; set; }
    public bool Cnj { get; set; }
    public bool JustMilUniao1grau { get; set; }
    public bool JustMilUniaoStm { get; set; }
    public bool JustMilEst1grau { get; set; }
    public bool JustMilEstTjm { get; set; }
    public bool JustElei1grau { get; set; }
    public bool JustElei2grau { get; set; }
    public bool JustEleiTse { get; set; }
    public string? ItemType { get; set; }
    public string? IncludedBy { get; set; }
    public DateTime? IncludedAt { get; set; }
    public string? UserIp { get; set; }
    public string? UpdatedBy { get; set; }
    public string? ProcedureId { get; set; }
    public string? ProcedureOrigin { get; set; }
    public bool? IsCriminal { get; set; } = false;
}
