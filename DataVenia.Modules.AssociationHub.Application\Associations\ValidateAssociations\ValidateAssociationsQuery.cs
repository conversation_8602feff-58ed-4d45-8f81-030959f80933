using DataVenia.Common.Application.Messaging;

namespace DataVenia.Modules.AssociationHub.Application.Associations.ValidateAssociations;

public sealed record ValidateAssociationsQuery(
    string EntityType,
    IReadOnlyCollection<string> TargetEntityTypes) : IQueryFr<AssociationValidationResponse>;

public sealed record AssociationValidationResponse(
    string EntityType,
    IReadOnlyCollection<string> ValidTargets,
    IReadOnlyCollection<string> InvalidTargets,
    IReadOnlyCollection<string> UnsupportedTargets,
    bool IsValid);
