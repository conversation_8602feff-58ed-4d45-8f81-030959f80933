﻿using DataVenia.Modules.Harvey.Domain.LawsuitClass;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace DataVenia.Modules.Harvey.Infrastructure.LawsuitClass;

public sealed class LawsuitClassRepository(HarveyDbContext context) : ILawsuitClassRepository
{
    public async Task<IReadOnlyCollection<Domain.LawsuitClass.LawsuitClass>> GetAllAsync(string? acronym, CancellationToken cancellationToken = default)
    {
        IQueryable<Domain.LawsuitClass.LawsuitClass> query = context.LawsuitClasses;

        if (!string.IsNullOrWhiteSpace(acronym))
            query = query.Where(c => c.Acronym.Contains(acronym));

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<Domain.LawsuitClass.LawsuitClass?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await context.LawsuitClasses.FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public void Add(Domain.LawsuitClass.LawsuitClass lawsuitClass)
    {
        context.LawsuitClasses.Add(lawsuitClass);
    }
}
