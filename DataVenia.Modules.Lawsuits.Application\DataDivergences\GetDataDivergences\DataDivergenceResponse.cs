using System.Text.Json;

namespace DataVenia.Modules.Lawsuits.Application.DataDivergences.GetDataDivergences;

public sealed record DataDivergenceResponse(
    Guid Id,
    Guid LawsuitId,
    Guid OfficeId,
    string InstanceId,
    Dictionary<string, DivergentFieldResponse> Fields,
    DateTime CreatedAt,
    DateTime? UpdatedAt,
    DateTime? AnalyzedAt,
    string? AnalyzedBy,
    bool? WasAccepted)
{
    public static DataDivergenceResponse FromDomain(Domain.Lawsuits.DataDivergence dataDivergence)
    {
        var fields = new Dictionary<string, DivergentFieldResponse>();
        
        foreach (var field in dataDivergence.Fields)
        {
            fields[field.Key] = new DivergentFieldResponse(
                field.Value.CurrentValue,
                field.Value.SuggestedValue);
        }

        return new DataDivergenceResponse(
            dataDivergence.Id,
            dataDivergence.LawsuitId,
            dataDivergence.OfficeId,
            dataDivergence.InstanceId,
            fields,
            dataDivergence.CreatedAt,
            dataDivergence.UpdatedAt,
            dataDivergence.AnalyzedAt,
            dataDivergence.AnalyzedBy,
            dataDivergence.WasAccepted);
    }
}

public sealed record DivergentFieldResponse(
    string CurrentValue,
    string SuggestedValue);
